﻿@model CreateManagementTeamFormViewModel

@{
    ViewData["Title"] = "Add Management Team";
}

<h5>
    <i class="bi bi-plus-circle-dotted"></i>
    Add a new management Team
</h5>

<form asp-controller="ManagementTeam" enctype="multipart/form-data">
    <div asp-validation-summary="All" class="text-danger" data-validation-summary="true"></div>
    <div class="row">
        <div class="col-md-6 mt-2">
            <div class="form-group">
                <label asp-for="TeamName" class="form-label mt-2"></label>
                <input type="text" class="form-control" asp-for="TeamName" placeholder="Team Name">
                <span asp-validation-for="TeamName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label>Select Managers</label>
                <select asp-for="SelectedManagementTeamManagers" asp-items="@(new MultiSelectList(Model.Employees, "Id", "EmployeeName", Model.SelectedManagementTeamManagers))"
                        class="form-control" multiple></select>
            </div>
            <div class="form-group">
                <label>Select Employees</label>
                <select asp-for="SelectedManagementTeamEmployees" asp-items="@(new MultiSelectList(Model.Employees, "Id", "EmployeeName", Model.SelectedManagementTeamEmployees))"
                        class="form-control" multiple></select>
            </div>
            <button type="submit" class="btn btn-primary mt-4">Save</button>
        </div>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}

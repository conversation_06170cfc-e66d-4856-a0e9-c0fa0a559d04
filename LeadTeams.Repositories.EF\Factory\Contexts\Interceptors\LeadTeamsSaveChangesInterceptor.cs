namespace LeadTeams.Repositories.EF.Factory.Contexts.Interceptors
{
    public class LeadTeamsSaveChangesInterceptor : SaveChangesInterceptor
    {
        private readonly IServiceProvider _serviceProvider;
        private SessionIdentity? sessionIdentity;
        private List<DataBaseEntity> _entitiesSaved = new List<DataBaseEntity>();
        private List<AuditEntry> auditEntries = new List<AuditEntry>();

        public LeadTeamsSaveChangesInterceptor(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
            DbContextEventData eventData,
            InterceptionResult<int> result,
            CancellationToken cancellationToken = default)
        {
            if (eventData.Context is null)
                return base.SavingChangesAsync(eventData, result, cancellationToken);

            var dbContext = eventData.Context;
            var authenticationValidationService = _serviceProvider.GetRequiredService<IAuthenticationValidationService>();
            sessionIdentity = authenticationValidationService.GetSessionIdentity();

            BeforeSaveChanges(dbContext);
            return base.SavingChangesAsync(eventData, result, cancellationToken);
        }

        public override async ValueTask<int> SavedChangesAsync(
            SaveChangesCompletedEventData eventData,
            int result,
            CancellationToken cancellationToken = default)
        {
            if (eventData.Context is null)
                return await base.SavedChangesAsync(eventData, result, cancellationToken);

            var dbContext = eventData.Context;
            await AfterSaveChangesCreateAuditAsync(dbContext, cancellationToken);

            dbContext.ChangeTracker.Clear();
            return await base.SavedChangesAsync(eventData, result, cancellationToken);
        }

        private void BeforeSaveChanges(DbContext dbContext)
        {
            dbContext.ChangeTracker.DetectChanges();
            BeforeSaveChangesSetBaseModelValues(dbContext);
            BeforeSaveChangesTrackEntitiesForDatabaseWatcher(dbContext);
            auditEntries = BeforeSaveChangesCreateAudit(dbContext);
        }

        private void BeforeSaveChangesSetBaseModelValues(DbContext dbContext)
        {
            if (sessionIdentity is null)
                return;

            foreach (var entry in dbContext.ChangeTracker.Entries<BaseModel>())
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedBy = sessionIdentity.EmployeeId;
                        entry.Entity.CreatedAt = DateTime.UtcNow;
                        break;
                    case EntityState.Deleted:
                        entry.Entity.DeletedBy = sessionIdentity.EmployeeId;
                        entry.Entity.DeletedAt = DateTime.UtcNow;
                        break;
                    case EntityState.Modified:
                        entry.Entity.ModifaiedBy = sessionIdentity.EmployeeId;
                        entry.Entity.ModifaiedAt = DateTime.UtcNow;
                        break;
                }
            }
        }

        private void BeforeSaveChangesTrackEntitiesForDatabaseWatcher(DbContext dbContext)
        {
            _entitiesSaved = dbContext.ChangeTracker.Entries()
                .Where(x => x.State != EntityState.Unchanged)
                .Select(x => new DataBaseEntity
                {
                    Entity = x.Entity,
                    EntityState = Enum.Parse<DataBaseEntityState>(x.State.ToString()),
                    EntityType = x.Entity.GetType().Name,
                }).ToList();
        }

        private List<AuditEntry> BeforeSaveChangesCreateAudit(DbContext dbContext)
        {
            if (sessionIdentity is null)
                return new List<AuditEntry>();

            var auditEntries = new List<AuditEntry>();

            foreach (var entry in dbContext.ChangeTracker.Entries().Where(IsAuditable))
            {
                var auditEntry = new AuditEntry
                {
                    TableName = entry.Entity.GetType().Name,
                    EmployeeId = sessionIdentity.EmployeeId,
                };
                var properties = entry.Properties.Where(IsAuditableProperty);

                foreach (var property in properties)
                {
                    string propertyName = property.Metadata.Name;

                    if (property.Metadata.IsPrimaryKey())
                    {
                        auditEntry.KeyValues[propertyName] = property.CurrentValue;
                        continue;
                    }

                    switch (entry.State)
                    {
                        case EntityState.Added:
                            auditEntry.AuditType = Enums.AuditType.Create;
                            auditEntry.NewValues[propertyName] = property.CurrentValue;
                            break;
                        case EntityState.Deleted:
                            auditEntry.AuditType = Enums.AuditType.Delete;
                            auditEntry.OldValues[propertyName] = property.OriginalValue;
                            break;
                        case EntityState.Modified:
                            if (!Equals(property.OriginalValue, property.CurrentValue))
                            {
                                auditEntry.AuditType = Enums.AuditType.Update;
                                auditEntry.ChangedColumns.Add(propertyName);
                                auditEntry.OldValues[propertyName] = property.OriginalValue;
                                auditEntry.NewValues[propertyName] = property.CurrentValue;
                            }
                            break;
                    }
                }

                auditEntries.Add(auditEntry);
            }

            return auditEntries;
        }

        private async Task AfterSaveChangesCreateAuditAsync(DbContext dbContext, CancellationToken cancellationToken)
        {
            if (sessionIdentity is null)
                return;

            if (!auditEntries.Any()) return;

            foreach (var entry in auditEntries)
            {
                if (entry.TempProperties != null)
                {
                    foreach (var prop in entry.TempProperties.Cast<PropertyEntry>())
                    {
                        if (prop.Metadata.IsPrimaryKey())
                        {
                            entry.KeyValues[prop.Metadata.Name] = prop.CurrentValue;
                        }

                        entry.NewValues[prop.Metadata.Name] = prop.CurrentValue;
                    }
                }
            }

            dbContext.Set<AuditModel>().AddRange(auditEntries.Select(a => a.ToAudit(sessionIdentity.OrganizationId)));
            await dbContext.SaveChangesAsync(cancellationToken);
        }

        private static bool IsAuditable(EntityEntry entry) =>
            entry.Entity is not AuditModel &&
            entry.State != EntityState.Detached &&
            entry.State != EntityState.Unchanged &&
            !entry.Entity.GetType().IsDefined(typeof(NotAuditableAttribute), true);

        private static bool IsAuditableProperty(PropertyEntry property) =>
            property.Metadata != null &&
            property.Metadata.PropertyInfo != null &&
            !property.Metadata.PropertyInfo.IsDefined(typeof(NotAuditableAttribute), true);
    }
}
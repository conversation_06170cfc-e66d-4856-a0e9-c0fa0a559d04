namespace LeadTeams.PermissionAndSession.Authentication
{
    public class AuthenticationHttpValidationService : IAuthenticationValidationService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AuthenticationHttpValidationService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public string GetStringToken()
        {
            string token = string.Empty;

            HttpContext? context = _httpContextAccessor.HttpContext;
            if (context is null)
                return token;

            if (context.Request.Headers.TryGetValue(AuthenticationDefaults.AuthenticationHeader, out var headersAuthToken) && !string.IsNullOrEmpty(headersAuthToken))
                token = headersAuthToken.ToString();
            else if (context.Request.Cookies.TryGetValue(AuthenticationDefaults.AuthenticationCookie, out var cookiesAuthToken) && !string.IsNullOrEmpty(cookiesAuthToken))
                token = cookiesAuthToken;

            var accessToken = token.Replace(AuthenticationDefaults.AuthenticationScheme + " ", "");
            return accessToken;
        }

        public JwtSecurityToken? GetSecurityToken()
        {
            var token = GetStringToken();
            if (string.IsNullOrEmpty(token))
                return null;

            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtToken = tokenHandler.ReadToken(token) as JwtSecurityToken;

            return jwtToken;
        }

        public bool IsTokenValid(JwtSecurityToken jwtSecurityToken)
        {
            if (jwtSecurityToken is null)
                return false;

            if (jwtSecurityToken.ValidTo < DateTime.UtcNow)
                return false;

            return true;
        }

        public void SetTokenToHeader(string token)
        {
            if (string.IsNullOrEmpty(token))
                return;

            HttpContext? context = _httpContextAccessor.HttpContext;
            if (context is null)
                return;

            context.Request.Headers[AuthenticationDefaults.AuthenticationHeader] = $"Bearer {token}";
        }

        public SessionIdentity GetSessionIdentity()
        {
            var jwtToken = GetSecurityToken();

            if (jwtToken is null)
            {
                HttpContext? context = _httpContextAccessor.HttpContext;
                if (context != null)
                {
                    IServiceProvider services = context.RequestServices;
                    SessionAppSettings sessionAppSettings = services.GetRequiredService<SessionAppSettings>();
                    return new SessionIdentity()
                    {
                        OrganizationId = sessionAppSettings.OrganizationId,
                    };
                }
                throw new UnauthorizedAccessException();
            }

            var claims = jwtToken.Claims;
            SessionIdentity sessionIdentity = new SessionIdentity()
            {
                OrganizationId = Ulid.Parse(claims.Single(x => x.Type == nameof(EmployeeLoginSessionResponse.Organization)).Value),
                EmployeeId = Ulid.Parse(claims.Single(x => x.Type == JwtRegisteredClaimNames.UniqueName).Value),
            };

            return sessionIdentity;
        }
    }
}

namespace LeadTeams.API.BackgroundJobs
{
    public class LoginSessionManager : BackgroundService
    {
        private readonly Serilog.ILogger _logger;
        private readonly PermissionAndSession.Session.ISession _session;
        private readonly IHubContext<LeadTeamsHub> _hubContext;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private LeadTeamsHub _leadTeamsHub;

        public LoginSessionManager(
            Serilog.ILogger logger,
            PermissionAndSession.Session.ISession session,
            IHubContext<LeadTeamsHub> hubContext,
            IServiceScopeFactory serviceScopeFactory)
        {
            _logger = logger;
            _session = session;
            _hubContext = hubContext;
            _serviceScopeFactory = serviceScopeFactory;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
                        var repository = scope.ServiceProvider.GetRequiredService<IRepository>();
                        var loginSessionService = scope.ServiceProvider.GetRequiredService<ILoginSessionService>();
                        _leadTeamsHub = new LeadTeamsHub(unitOfWork, repository, loginSessionService, _logger);

                        await _leadTeamsHub.PingClientsAsync(_hubContext);

                        var statuses = LeadTeamsHub.GetClientHealthStatus(TimeSpan.FromSeconds(60));
                        var loginSessions = await loginSessionService.GetActiveLoginSessionsAsync();

                        foreach (var status in statuses)
                        {
                            var loginSession = loginSessions.FirstOrDefault(x => x.Id == status.employeeId);
                            if (loginSession != null)
                            {
                                loginSessions.Remove(loginSession);
                                continue;
                            }
                        }

                        foreach (var session in loginSessions)
                        {
                            _logger.Warning($"Session {session.SessionToken} for user {session.EmployeeId} is inactive. Removing session.");
                            session.SessionStatus = SessionStatusEnumeration.Inactive.Name;
                            await loginSessionService.UpdateLoginSession(session);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "An error occurred in the LoginSessionManager background job.");
                }
            }
        }
    }
}

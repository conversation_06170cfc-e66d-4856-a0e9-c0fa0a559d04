﻿namespace LeadTeams.Services.Server.Business
{
    public class ManagementTeamService : LeadTeamsBaseService<ManagementTeamModel, ManagementTeamModel, CreateManagementTeamViewModel, UpdateManagementTeamViewModel>, IManagementTeamService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ManagementTeamService(IAuthenticationValidationService authenticationValidationService, IUnitOfWork unitOfWork) : base(authenticationValidationService, unitOfWork.ManagementTeam)
        {
            _unitOfWork = unitOfWork;
        }

        protected override Func<IQueryable<ManagementTeamModel>, IIncludableQueryable<ManagementTeamModel, object>> Includes =>
            x => x
            .Include(xx => xx.ManagementTeamEmployees)
            .Include(xx => xx.ManagementTeamManagers);

        public override void ValidateEntity(ManagementTeamModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);

            if (model.ManagementTeamEmployees == null || !model.ManagementTeamEmployees.Any())
            {
                throw new ValidationException("Management team employees cannot be empty.");
            }

            if (model.ManagementTeamManagers == null || !model.ManagementTeamManagers.Any())
            {
                throw new ValidationException("Management team managers cannot be empty.");
            }

            if (model.ManagementTeamEmployees.Any(x => x.EmployeeId == Ulid.Empty))
            {
                throw new ValidationException("Management team employees cannot contain empty employee IDs.");
            }

            if (model.ManagementTeamManagers.Any(x => x.ManagerId == Ulid.Empty))
            {
                throw new ValidationException("Management team managers cannot contain empty manager IDs.");
            }

            if (model.ManagementTeamEmployees.Select(x => x.EmployeeId).Distinct().Count() != model.ManagementTeamEmployees.Count)
            {
                throw new ValidationException("Management team employees cannot contain duplicate employee IDs.");
            }

            if (model.ManagementTeamManagers.Select(x => x.ManagerId).Distinct().Count() != model.ManagementTeamManagers.Count)
            {
                throw new ValidationException("Management team managers cannot contain duplicate manager IDs.");
            }

            if (model.ManagementTeamEmployees.Any(x => model.ManagementTeamManagers.Select(xx => xx.ManagerId).Contains(x.Id)))
            {
                throw new ValidationException("Management team employees cannot be managers in the same team.");
            }

            if (model.ManagementTeamManagers.Any(x => model.ManagementTeamEmployees.Select(xx => xx.EmployeeId).Contains(x.Id)))
            {
                throw new ValidationException("Management team managers cannot be employees in the same team.");
            }
        }

        public List<EmployeeModel> SelectiveTeamManagerList() => _unitOfWork.Employee.GetAll();
        public List<EmployeeModel> SelectiveTeamEmployeeList() => _unitOfWork.Employee.GetAll();

        public List<EmployeeModel> GetTeamManagersForEmployee(Ulid employeeId) => _unitOfWork.ManagementTeamManager.GetAsEmployees(employeeId);
        public List<EmployeeModel> GetTeamEmployeesForEmployee(Ulid employeeId) => _unitOfWork.ManagementTeamEmployee.GetAsEmployees(employeeId);
    }
}
